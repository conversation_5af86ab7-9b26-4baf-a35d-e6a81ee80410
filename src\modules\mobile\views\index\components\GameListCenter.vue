<script>
import {menu} from "@/mixins/menu";
import {check} from "@/mixins/check";
import GameListItem from "@/modules/mobile/views/index/components/GameListItem.vue";
import {game} from "@/mixins/game";
import VenderListItem from '@/modules/mobile/views/index/components/VenderListItem.vue'

export default {
  name: "GameListCenter",
  components: {
    VenderListItem,
    GameListItem},
  mixins: [menu, check, game],
};
</script>

<template>
  <div class="game-list-center-container">
    <div class="select-type">
      <div class="title-group">
        <div class="game-title">
          <div class="title-content">
            <div class="title-text">
<!--              <img
                  class="game-icon rng"
                  style="width: auto"
                  :src="menuOptions[$store.state.platform.currentCategory].icon"
                  alt=""
              />-->
              <span>{{
                menuOptions[$store.state.platform.currentCategory].title
              }}</span>
            </div>
          </div>
        </div>
      </div>
<!--      <div class="sub-group">
        <div class="select-btn">
          <svg class="am-icon am-icon-icon-hot am-icon-md filter-icon">
            <use xlink:href="#icon-hot"></use>
          </svg>
        </div>
        <div class="select-btn">
          <svg class="am-icon am-icon-icon-recent am-icon-md filter-icon">
            <use xlink:href="#icon-recent"></use>
          </svg>
        </div>
        <div class="select-btn">
          <svg class="am-icon am-icon-icon-fav am-icon-md filter-icon">
            <use xlink:href="#icon-fav"></use>
          </svg>
        </div>
        <div class="select-btn">
          <svg class="am-icon am-icon-icon-search am-icon-md filter-icon">
            <use xlink:href="#icon-search"></use>
          </svg>
        </div>
      </div>-->
    </div>
    <!--    <div class="filter-game-container">
      <div class="filter_title">Categoria</div>
      <svg class="am-icon am-icon-btn-close am-icon-md close_filter">
        <use xlink:href="#btn-close"></use>
      </svg>
      <div class="search-game">
        <svg class="am-icon am-icon-search am-icon-md search-icon">
          <use xlink:href="#search"></use>
        </svg>
        <input
          type="text"
          class="search-input"
          autocomplete="off"
          placeholder="Procurar"
          value=""
        />
      </div>
      <div class="vassalage-box">
        <div class="category_title">Provedores</div>
        <div class="filter_item">
          <span class="classactive">Tudo</span><span class="">PG</span
          ><span class="">PP</span><span class="">JILI</span
          ><span class="">FC</span><span class="">JDB</span
          ><span class="">CQ9</span><span class="">BGS</span
          ><span class="">GPI</span><span class="">Habanero</span
          ><span class="">PNG</span><span class="">FunTa Gaming</span
          ><span class="">YOU LIAN GAMING</span><span class="">VirtualTech</span
          ><span class="">BoomingGames</span
          ><span class="">Ameba Entertainment</span><span class="">RiCH88</span
          ><span class="">Bigpot</span><span class="">Spadegaming</span
          ><span class="">Ultimate Play Games</span
          ><span class="">AsiaGaming</span
          ><span class="">Mega Entertainment</span
          ><span class="">Creative Gaming</span><span class="">Wazdan</span
          ><span class="">MNC</span><span class="">Spribe</span
          ><span class="">MAS</span><span class="">MicroGaming</span>
        </div>
      </div>
      <span class="filter_submit">Confirme</span
      ><span class="reset">Redefinir</span>
    </div>-->
    <div class="game-select" v-draggable style="position: static">
      <div class="select-bar hide-scrollbar">
        <div
            class="select-btn"
            :class="{ on: 0 === filter.platformId }"
            @click="updateFilter(0)"
        >
          {{ $t("All") }}
        </div>
        <div
            class="select-btn"
            v-for="item in platforms"
            :key="item.platformId"
            :class="{ on: item.platformId === filter.platformId }"
            @click="updateFilter(item.platformId)"
        >
          {{ item.platformName }}
        </div>
      </div>
    </div>
    <div class="slots-games">
      <div class="game-list-wrapper">
        <div class="game-list" v-if="$store.state.platform.currentCategory === '-2'">
          <template v-for="(item, index) in $store.state.platform.favoriteGames">
            <GameListItem :game="item" :key="index"/>
          </template>
        </div>
        <van-list
            v-model="loading"
            :finished="finished"
            finished-text=""
            @load="this.loadGame"
        >
          <div class="game-list">
            <template v-if="vendorArr.includes($store.state.platform.currentCategory)">
              <template  v-for="(item, index) in list">
                <VenderListItem :game="item" :key="index" />
              </template>
            </template>
            <template v-else>
              <GameListItem v-for="(item, index) in list" :game="item" :key="index"/>
            </template>
          </div>
          <template #loading>
            <div class="scroll-loading">
              <svg
                  class="loading-icon"
                  x="0px"
                  y="0px"
                  width="40px"
                  height="40px"
                  viewBox="0 0 40 40"
              >
                <path
                    opacity="0.2"
                    d="M20.201,5.169c-8.254,0-14.946,6.692-14.946,14.946c0,8.255,6.692,14.946,14.946,14.946 s14.946-6.691,14.946-14.946C35.146,11.861,28.455,5.169,20.201,5.169z M20.201,31.749c-6.425,0-11.634-5.208-11.634-11.634 c0-6.425,5.209-11.634,11.634-11.634c6.425,0,11.633,5.209,11.633,11.634C31.834,26.541,26.626,31.749,20.201,31.749z"
                ></path>
                <path
                    d="M26.013,10.047l1.654-2.866c-2.198-1.272-4.743-2.012-7.466-2.012h0v3.312h0 C22.32,8.481,24.301,9.057,26.013,10.047z"
                >
                  <animateTransform
                      attributeType="xml"
                      attributeName="transform"
                      type="rotate"
                      from="0 20 20"
                      to="360 20 20"
                      dur="0.5s"
                      repeatCount="indefinite"
                  ></animateTransform>
                </path>
              </svg>
            </div>
          </template>
        </van-list>
      </div>
    </div>
<!--    <div id="page_bg" class="common"></div>-->
  </div>
</template>

<style scoped>
::v-deep li {
  list-style: none;
}

.game-fav {
  position: absolute;
  right: 0;
  top: 0;
  width: 0.45rem;
  height: 0.45rem;
  border-radius: 0 0.16rem 0 0.16rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>