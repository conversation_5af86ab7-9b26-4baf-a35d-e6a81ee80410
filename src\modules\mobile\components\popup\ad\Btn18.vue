<script>
import {TDTC_ROURE} from "@/api/tdtc";

export default {
  mounted() {
    this.query206()
  },
  methods: {
    query206() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_ACTIVE203_URL_Ad)
          .then((res) => {
            iconsole(res)
            // VipActiveInfo
            Object.assign(this.res, res)
          })
          .catch(() => {
          })
    },
  }
}
</script>

<template>
  <div>18</div>
</template>

<style scoped>


</style>