{"name": "wp-bra", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --mode development", "test": "vue-cli-service build --mode development", "build": "vue-cli-service build --mode production"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 11"], "dependencies": {"@lucky-canvas/vue": "^0.1.11", "@vant/touch-emulator": "^1.4.0", "ant-design-vue": "^1.7.8", "axios": "^1.3.5", "babel-polyfill": "^6.26.0", "bootstrap": "^5.3.2", "clipboard": "^2.0.11", "core-js": "^3.34.0", "crypto-js": "^4.1.1", "currency.js": "^2.0.4", "daterangepicker": "^3.1.0", "firebase": "^11.1.0", "jquery": "^3.7.1", "lodash": "^4.17.21", "long": "^5.2.3", "moment": "^2.29.4", "protobufjs": "^7.2.4", "regenerator-runtime": "^0.14.0", "register-service-worker": "^1.7.2", "v-lazy": "^0.9.5", "vant": "^2.13.0", "vee-validate": "^2.2.15", "vue": "^2.6.14", "vue-awesome-swiper": "^4.1.1", "vue-count-to": "^1.0.13", "vue-i18n": "^8.28.2", "vue-js-modal": "^1.3.34", "vue-qr": "^4.0.9", "vue-router": "^3.5.1", "vue-seamless-scroll": "^1.1.23", "vuejs-paginate": "^2.1.0", "vuex": "^3.6.2", "vuex-persistedstate": "^4.1.0", "webpack": "^5.88.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "babel-plugin-import": "^1.13.8", "babel-plugin-transform-remove-console": "^6.9.4", "mockjs": "^1.1.0", "moment-locales-webpack-plugin": "^1.2.0", "sass": "^1.32.7", "sass-loader": "^12.0.0", "unplugin-vue-components": "^0.25.2", "vue-template-compiler": "^2.6.14"}}