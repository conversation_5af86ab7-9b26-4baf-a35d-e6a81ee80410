<script>
import RecordBoard from "@/modules/mobile/components/RecordBoard.vue";
import {FieldRenderType} from "@/utils/common";
import {TDTC_ROURE} from "@/api/tdtc";

export default {
  components: {RecordBoard},
  data() {
    return {
      column: [
        {
          label: this.$t('ad.panel.9.th.0'),
          prop: "rank",
        },
        {
          label: this.$t('ad.panel.9.th.1'),
          prop: "need_spread_num",
          render: FieldRenderType.customTemplate,
          customTemplate: this.customTemplate,
        },
        {
          label: this.$t('ad.panel.9.th.2'),
          prop: "award",
          render: FieldRenderType.formatGold,
        },
      ],
      conf: []
    }
  },
  mounted() {
    this.query37()
  },
  methods: {
    customTemplate(v) {
      return "≥ " + v
    },
    query37() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_AGENT_DAILY_RANK_CONF)
          .then((res) => {
            if (res['data']) this.conf = res['data']
          })
          .catch(() => {
          })
    },
  }
}
</script>

<template>
  <div class="ad-wrap">
    <div class="ad-bg">
      <img src="/img/tdtc/events/9.png" style="width: 5rem;margin-top: .6rem;" alt="">
    </div>
    <div class="ad-title">{{ $t('ad.tab.9') }}</div>
    <div>
      <RecordBoard :data="conf" :column="column"/>
      <div class="ad-btn" @click="$router.push({path:'/m/inviteFriends',query:{index: 5}})">{{ $t('go') }}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">

::v-deep .record-board {
  .record-board-wrap {
    z-index: 1;
    width: 6.19rem;
    height: 5rem;
    border-radius: 0.12rem;
    color: #CB542B;

    table {
      background: #FFFFFF;
      table-layout: unset;

      th {
        font-size: 0.26rem;
        color: #FFFFFF;
        background: #A46BFF;
        padding: unset;
        width: unset;
      }

      tr {
        line-height: 0.6rem;
      }
    }
  }
}

.ad-wrap {
  border-radius: 0.18rem;
  height: 8.8rem;
  margin-top: .05rem;
  background: linear-gradient(180deg, rgba(255, 109, 109, 1), rgba(255, 255, 255, 1));
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: .15rem .2rem;

  font-size: 0.26rem;
  color: #FFFFFF;
  text-align: center;

  .ad-title {
    font-weight: bold;
    font-size: 0.32rem;
  }

  .ad-bg {
    position: fixed;
    top: 0;
  }


  .ad-btn {
    width: 6.19rem;
    height: 0.58rem;
    line-height: 0.58rem;
    background: linear-gradient(90deg, rgba(235, 127, 62, 1), rgba(249, 182, 6, 1));
    border-radius: 0.12rem;
    margin-top: .2rem;
  }
}

</style>