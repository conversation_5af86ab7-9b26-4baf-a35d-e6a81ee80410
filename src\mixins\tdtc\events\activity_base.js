export const activity_base = {
    data() {
        return {
            currentActivity: {
                activityType: 0,
                awardType: 0,
                status:0,
                withdrawRate: 0
            },
        };
    },
    mounted() {
        Object.assign(this.currentActivity, this.$store.state.activitySwitchDetails.find(item => item.activityType === parseInt(this.$route.path.split('/').pop(), 10)))
    },
}