<script>
import FieldRender from "@/modules/mobile/components/FieldRender.vue";

export default {
  name: "RecordBoard",
  components: {FieldRender},
  props: {
    column: {
      type: Array,
      required: true,
    },
    data: {
      type: Array,
      required: true,
      default: ()=>[]
    },
    hasMore: {
      default: false
    },
  },
}
</script>

<template>
  <div class="record-board" style="overflow: hidden;">
    <div v-draggable v-if="column.length < 4">
      <div class="record-board-wrap">
        <table>
          <thead>
          <tr>
            <th v-for="item in column" :style="item['style']">{{ item.label }}</th>
          </tr>
          </thead>
          <tbody v-if="data.length">
          <tr v-for="(row,index) in data">
            <td v-for="item in column">
              <span v-if="item.index">{{ index + 1 }}</span>
              <field-render v-else :value="row[item.prop]" :field="item"/>
            </td>
          </tr>
          </tbody>
          <van-empty v-else style="margin: auto;width: 7.5rem"/>
        </table>
        <div v-if="hasMore" class="hasMore" @click="$emit('loadingMore')"><van-icon name="arrow-down" class="i" color="#ffb627" size=".5rem"/></div>
      </div>
    </div>
    <ul style="padding: 0.1rem .2rem 0" v-else>
      <template v-if="data.length">
        <li style="
background: linear-gradient(0deg, rgba(255, 251, 244, 1), rgba(255, 255, 254, 1));
background: #FFFFFF;
border-radius: 0.08rem;
border: 0.01px solid rgba(149,149,149,0.99);
border: 0.01px solid #CECDC9;
padding: 0.1rem .15rem;margin-bottom: .2rem;line-height: .5rem;" v-for="(row,index) in data" >

          <div v-for="item in column" style="display: flex;flex-direction: row;justify-content: space-between;align-items: center;font-size: 0.26rem;
color: #312E2A;">
            <div>{{item.label}}</div>
            <span v-if="item.index">{{ index + 1 }}</span>
            <field-render v-else :value="row[item.prop]" :field="item"/>
          </div>
        </li>
      </template>
      <van-empty v-else/>
    </ul>
    <div v-if="hasMore && column.length >= 4" class="hasMore" @click="$emit('loadingMore')"><van-icon name="arrow-down" class="i" color="#ffb627" size=".5rem"/></div>
  </div>
</template>

<style scoped lang="scss">
.v-base .am-list-body table, .v-base tbody, .v-base td, .v-base tfoot, .v-base th, .v-base thead, .v-base time, .v-base tr, .v-base tt {
  border-width: 0 !important;
}

.record-board {
  margin-top: .1rem;
  position: relative;
  //text-align: center;
  color: #5c5b66e6;

  .record-board-wrap {
    overflow: scroll;
    position: relative
  }

  table {
    width: 100%;
    table-layout: fixed;
    text-align: center;

    thead > tr {
      position: sticky;
      top: -0.01rem;
      color: #FF5C00;
      background: #F9F9F9;
    }
    th {
      font-size: .26rem;
      width: 2.5rem;
    }

    tr {
      height: .68rem;
      line-height: .68rem;
    }

    td {
      font-size: .23rem;
    }
  }

  .hasMore {
    .i {
      display: inline-block;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      background-size: 100% auto;
      animation: flash 3s;
      animation-iteration-count: infinite;
    }
  }
}
</style>