<!DOCTYPE html>
<html data-theme="dark">
<head>
    <meta
            http-equiv="Content-Type"
            content="text/html; charset=UTF-8"/>
    <meta
            name="viewport"
            content="viewport-fit=cover,width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no"/>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Cache" content="no-cache">
    <title>
        TDTC | Online Slots | PGSlot | Slots | SlotBet | Fish Game | Card
        Games | online casino | Slot Casino | Sports Betting
    </title>
    <meta name="apple-mobile-web-app-capable" content="yes">
<!--    <meta name="apple-mobile-web-app-status-bar-style" content="default">-->

    <link rel="shortcut icon" href="img/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" sizes="57x57" href="img/icon/57.png">
    <link rel="apple-touch-icon" sizes="72x72" href="img/icon/72.png">
    <link rel="apple-touch-icon" sizes="114x114" href="img/icon/114.png">
    <link rel="apple-touch-icon" sizes="144x144" href="img/icon/144.png">
    <link rel="apple-touch-icon" sizes="180x180" href="img/icon/180.png">
    <meta
            name="title"
            content="TDTC | Online Slots | PGSlot | Slots | SlotBet | Fish Game | Card Games | online casino | Slot Casino | Sports Betting"/>
    <meta
            name="description"
            content="TDTC is an online games platform that operates in various countries. It offers services such as sports betting, online casino games, poker, and more. It is licensed and regulated by various authorities to ensure fair play and responsible gambling"/>
    <style>
        html {
            background-color : #ccc !important;
        }
        * {
            moz-user-select: -moz-none;
            -moz-user-select: none;
            -o-user-select: none;
            -khtml-user-select: none;
            -webkit-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        .van-toast {
            min-width: 80px !important;
            width: unset !important;
            word-break: break-word !important;
        }

        input[type="number"]::-webkit-inner-spin-button,
        input[type="number"]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
        input[type="number"] {
            -moz-appearance: textfield;
        }

        .discount, .promotion, .vip {
            position          : relative;
            min-width         : 23%;
            --tw-text-opacity : 1;
            color             : rgb(255 255 255 / var(--tw-text-opacity))
        }

        .discount i, .promotion i, .vip i {
            display           : block;
            width             : 30px;
            height            : 30px;
            margin            : 0 auto 5px;
            background-image  : url(m/icons/discount.33d9133b06ee6a9d.png);
            background-repeat : no-repeat;
            background-size   : contain
        }

        .promotion i {
            background-image : url(m/icons/gift.2cf73743b576eaa8.png)
        }

        .vip i {
            background-image : url(m/icons/vip.86d83156c0884e8b.png)
        }

        .balance-button {
            display         : inline-block;
            vertical-align  : middle;
            width           : 12px;
            height          : 12px;
            background      : url(m/icons/loop.8890749fc9e66a75.png) no-repeat center;
            background-size : contain
        }
    </style>
    <% if (process.env.VUE_APP_CAPTCHA_PLATFORM === '3' ) { %>
    <script>
        window.AliyunCaptchaConfig = {
            region: "sgp",
            prefix: 'n0c6usb',
        };
    </script>
    <script type="text/javascript" src="https://o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js">
    </script>
    <% } %>
    <% if (process.env.VUE_APP_CAPTCHA_PLATFORM === '2' ) { %>
    <script src="https://ca.turing.captcha.qcloud.com/TCaptcha-global.js"></script>
    <% } %>
    <% if (process.env.VUE_APP_CAPTCHA_PLATFORM === '1' ) { %>
    <script src="./js/gt4.js"></script>
    <% } %>
    <script>
        (function(window) {

            if(undefined !== window.TextEncoder) {return;}

            function _TextEncoder() {
                //--DO NOTHING
            }
            _TextEncoder.prototype.encode = function(s) {
                return unescape(encodeURIComponent(s)).split('').map(function(val) {return val.charCodeAt();});
            };
            function _TextDecoder() {
                //--DO NOTHING
            }
            _TextDecoder.prototype.decode = function(code_arr) {
                return decodeURIComponent(escape(String.fromCharCode.apply(null, code_arr)));
            };

            window.TextEncoder = _TextEncoder;
            window.TextDecoder = _TextDecoder;

        })(this);

    </script>
    <script>
        let script = document.createElement("script");
        script.type = "text/javascript";
        script.src = "./js/firebase.js?v="+ new Date();
        document.head.appendChild(script);
    </script>
</head>

<body class="hide_scroll_bar">
<script>
    let v = new Date().getTime()
    function loadCSS(href, disable = false, id = '') {
        let cssLink = document.createElement("link");
        cssLink.rel = "stylesheet";
        cssLink.type = "text/css";
        cssLink.href = href+'?v='+v;
        if (disable) {
            cssLink.disabled = true
        }
        if (id) {
            cssLink.id = id
        }
        // cssLink.rel = 'preload';
        // cssLink.as = 'style';
        // cssLink.href = href;
        document.head.appendChild(cssLink);
    }
    function loadJS(src) {
        let script = document.createElement("script");
        script.type = "text/javascript";
        script.src = src;
        document.head.appendChild(script);
    }
    function loadFileIntoBody(filePath) {
        fetch(filePath+'?v='+v)
            .then(response => response.text())
            .then(html => {
                let parser = new DOMParser();
                let xmlDoc = parser.parseFromString(html, "text/xml");
                document.body.insertBefore(document.importNode(xmlDoc.documentElement, true), document.body.firstChild);
            })
            .catch(error => {
                console.error('Error loading HTML:', error);
            });
    }
    loadCSS("./css/bootstrap.min.css");
    loadFileIntoBody("img/common.svg");
    // if (/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent)) {
        loadCSS("./css/mobile.common.css");
        loadCSS("./m/app.6217f5e3.css");
        loadCSS("./m/splash.185262b1.css");
        loadCSS("./mobile/mc/memberCenter.9b835f05.css");
        loadFileIntoBody("img/mobile.svg");
    /*} else {
        loadCSS("./css/chunk-web-view.913bd2bc.css");
        loadCSS("./css/chunk-vendors.357cd43c.css");
        loadCSS("./css/index.87c61bbc.css");
        loadCSS("./ac/v.1.0.1/manifest/ac_shanshan.d3349f54.css");
        loadCSS("./mc/v.1.0.1/manifest/mc_shanshan.9d418ec2.css");
        loadCSS("./common/v.1.0.1/manifest/lib.core.min.3234269c.css");
        loadFileIntoBody("img/pc.svg");
    }*/
    loadCSS("css/main.css", true, "css_forgot_main");
    loadCSS("css/ant.css", true, "css_forgot_ant");
    loadCSS("css/3077.css", true, "css_forgot_3077");
    loadCSS("css/test.css", true, "css_forgot_test");
</script>
<div id="captcha-button"></div>
<div id="captcha-element"></div>
<div id="app"></div>
<div
        class="loading-img-container"
        style="display: none"></div>
<span id="root"></span>
<script>

    // if (/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent)) {
        // prettier-ignore
        !function (e, t) {
            var n = t.documentElement
                , i = e.devicePixelRatio || 1
                , a = 7.5
                , r = 16;

            n.style.setProperty("--theme-max-width", "100%");
            let maxWidth = 499;
            if (!/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent)) {
                n.style.setProperty("width", maxWidth + "px");
                n.style.setProperty("max-width", maxWidth + "px");
                n.style.setProperty("margin", "0 auto");
                n.style.setProperty("--theme-max-width", maxWidth + "px");
            }

            function d() {
                let clientWidth = /Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent) ? n.clientWidth : maxWidth;
                var e = clientWidth / a;
                n.style.fontSize = e + "px"
            }

            if (i >= 2) {
                var o = t.createElement("body")
                    , c = t.createElement("div");
                c.style.border = ".5px solid transparent",
                    o.appendChild(c),
                    n.appendChild(o),
                1 === c.offsetHeight && n.classList.add("hairlines"),
                    n.removeChild(o)
            }
            !function e() {
                if (t.body) {
                    let clientWidth = /Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent) ? n.clientWidth : maxWidth;
                    var i = clientWidth / 100 / (a / 2);
                    t.body.style.fontSize = r * i + "px"
                } else
                    t.addEventListener("DOMContentLoaded", e)
            }(),
                d(),
                e.addEventListener("resize", d),
                e.addEventListener("pageshow", function (t) {
                    var n = !1;
                    try {
                        if (e.performance) {
                            const t = e.performance.getEntriesByType("navigation")
                                , i = !!t[0] && "back_forward" === t[0].type
                                , a = !!e.performance.navigation && 2 === e.performance.navigation.type;
                            n = i || a
                        }
                    } catch (e) {
                    }
                    (t.persisted || n) && d()
                })
        }(window, document);

        !(function () {
            var e = 0.01 * window.innerHeight,
                t = document.documentElement,
                n = e + "px";
            t.style.getPropertyValue("--vh") !== n &&
            t.style.setProperty("--vh", n);
        })();
    // } else {
    //
    // }
</script>
</body>
</html>
