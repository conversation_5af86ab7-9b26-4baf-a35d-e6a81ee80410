<script>
import { play } from "@/mixins/play";
import {check} from '@/mixins/check'

export default {
  name: "home-header",
  data() {
    return {
      showPopover: false,
    };
  },
  mounted() {
    this.showPopover = false


  },
  mixins: [play, check],
};
</script>

<template>
<!--  <div class="mc-header-wrap" style="background-color: rgb(1,1,1);">
    <div
        id="mc-header"
        class="mc-navbar-blue mc-withdraw am-navbar am-navbar-light"
    >
&lt;!&ndash;      <video  autoplay loop muted playsinline style="width: 100%;height: 100%; object-fit: cover">
        <source type="video/mp4" src="/video/header.mp4">
      </video>&ndash;&gt;
      <img src="/video/1231.gif" alt="" style="width: 100%;height: 100%; object-fit: cover">
    </div>
    <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
  </div>-->

  <div class="home-header">
    <div class="header-content" style="background-image: url('/img/home_header.png');background-repeat: no-repeat;background-size: cover">
      <img src="/img/home_logo.png?v=1" alt="" style="height: 1.2rem;position: absolute; top: .1rem;left: .1rem"/>
      <div class="header-left">
<!--        <div class="header-menu" @click="$store.commit('setShowMobileSide')" :class="{ off: $store.state.showMobileSide }">-->
<!--          <svg class="am-icon am-icon-icon-nav am-icon-md">-->
<!--            <use xlink:href="#icon-nav"></use>-->
<!--          </svg>-->
<!--        </div>-->
<!--        <div class="header-logo">-->
<!--          <img src="/img/home_logo.png" alt="" style="height: .9rem"/>-->
<!--        </div>-->
      </div>
      <div class="header-right">

        <div class="user-info">
<!--          -->
          <div class="user-balance">
            <div class="deposit-btn" @click="$store.commit('setBottomModalStatus', 1)" v-if="!$store.getters.inJsBridge && isMobile && ((isIos && !standalone) || downUrl)">
              <svg style="fill: #F6921F" t="1726820666299" class="am-icon am-icon-icon-deposit am-icon-md" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4816" width="200" height="200"><path d="M499.809524 780.190476s295.740952-271.11619 316.952381-292.571428a30.72 30.72 0 0 0-24.380953-48.761905h-170.666666V48.761905a48.761905 48.761905 0 0 0-48.761905-48.761905h-146.285714a48.761905 48.761905 0 0 0-48.761905 48.761905v390.095238H207.238095a29.500952 29.500952 0 0 0-24.380952 48.761905c27.550476 27.550476 316.952381 292.571429 316.952381 292.571428z m463.238095-121.904762a48.761905 48.761905 0 0 0-48.761905 48.761905v219.428571H109.714286v-219.428571a48.761905 48.761905 0 0 0-97.52381 0v268.190476a48.761905 48.761905 0 0 0 48.761905 48.761905h902.095238a48.761905 48.761905 0 0 0 48.761905-48.761905V707.047619a48.761905 48.761905 0 0 0-48.761905-48.761905z" p-id="4817"></path></svg>
            </div>
            <div class="withdraw-btn" v-if="$store.state.configs.customer_web" @click="goUrl($store.state.configs.customer_web)">
              <svg style="fill: #F6921F" t="1726820769449" class="am-icon am-icon-icon-withdraw am-icon-md" viewBox="0 0 1137 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5130" width="200" height="200"><path d="M716.640611 546.241413a56.876022 56.876022 0 0 0 56.876023-56.876022v-91.001636a56.876022 56.876022 0 0 0-113.752045 0v91.001636a56.876022 56.876022 0 0 0 56.876022 56.876022z m-295.755316-204.75368a56.876022 56.876022 0 0 0-56.876022 56.876022v91.001636a56.876022 56.876022 0 0 0 113.752045 0v-91.001636a56.876022 56.876022 0 0 0-56.876023-56.876022z m622.451188 91.001635a476.621067 476.621067 0 0 0-949.147059 0A101.921832 101.921832 0 0 0 0.002731 534.866208v136.502454A102.149336 102.149336 0 0 0 102.379571 773.745502H159.255593a45.500818 45.500818 0 0 0 45.500818-45.500818V477.990186a45.500818 45.500818 0 0 0-42.088257-45.500818 409.50736 409.50736 0 0 1 812.189598 0A45.500818 45.500818 0 0 0 932.769496 477.990186v250.254498a45.500818 45.500818 0 0 0 18.427831 36.400654c-30.258044 38.448191-81.673968 97.37175-132.179876 122.852209a692.749951 692.749951 0 0 1-143.100072 53.690965A56.421014 56.421014 0 0 0 625.638976 910.247955h-68.251227a56.876022 56.876022 0 0 0 0 113.752045h68.251227a57.558535 57.558535 0 0 0 51.870932-33.443101A584.002997 584.002997 0 0 0 841.76786 932.998364a558.522539 558.522539 0 0 0 174.723141-159.252862h18.655335a102.149336 102.149336 0 0 0 102.37684-102.37684v-136.502454a101.921832 101.921832 0 0 0-94.186693-102.37684z" p-id="5131"></path></svg>
            </div>
          </div>
        </div>



<!--        <div class="user-info" v-if="!$store.getters.isLogin">
          <div class="user-balance" @click.stop="refreshBalance">
            <div class="member-balance">
              <div class="sum-balance">
                <span class="symbol">{{ this.$store.state.configs.currency_symbol }}</span>
                <span class="amount">{{ balance }}</span>
              </div>
              <div class="balance-control">
                <div class="refresh-balance" :class="{rotateFull: refresh}">
                  <svg class="am-icon am-icon-return am-icon-md">
                    <use xlink:href="#return"></use>
                  </svg>
                </div>
              </div>
            </div>
            <div class="deposit-btn" @click.stop="$router.push('/m/voucherCenter')">
              <svg class="am-icon am-icon-icon-deposit am-icon-md">
                <use xlink:href="#icon-deposit"></use>
              </svg>
            </div>
            <div class="withdraw-btn" @click.stop="$router.push('/m/withdraw')">
              <svg class="am-icon am-icon-icon-withdraw am-icon-md">
                <use xlink:href="#icon-withdraw"></use>
              </svg>
            </div>
          </div>
          <van-popover get-container=".header-content" placement="bottom-end" overlay theme="dark" v-model="showPopover" trigger="click">
            <div v-if="showPopover"
                class="sidebar main-nav-open" style="top: unset; right: 0.01rem"
            >
              <div class="right-side-menu hide-scrollbar">
                <div class="rightbar-content">
                  <div class="user-info-wrap">
                    <div class="user-info">
                      <div class="member-wrap">
                        <div class="profile-icon">
                          <img
                              :src="`img/profile/icon_${$store.state.account.icon}.png`"
                              alt=""
                          />
                          &lt;!&ndash; react-empty: 3184 &ndash;&gt;
                        </div>
                        <span class="wrap-text">VIP{{ $store.state.account.vip }}</span>
                      </div>
                      <div class="user-balance">
                        <div class="member">{{ $store.state.account.nickname }}</div>
                        <div class="member-id">
                          &lt;!&ndash; react-text: 3152 &ndash;&gt;ID:{{ $store.state.account.userId }}&lt;!&ndash; /react-text &ndash;&gt;
                          <svg class="am-icon am-icon-icon-copy am-icon-md copy-btn" :data-clipboard-text="$store.state.account.userId">
                            <use xlink:href="#icon-copy"></use>
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="member-nav">
                    <div class="member-nav-list">
                      <div class="member-nav-item" @click="$router.push('/m/securityCenter')">
                        <div class="icon-wrap">
                          <svg class="am-icon am-icon-icon-account am-icon-md">
                            <use xlink:href="#icon-account"></use>
                          </svg>
                        </div>
                        <p class="member-nav-name">{{ $t('in_my_account') }}</p>
                      </div>
                      <div class="member-nav-item" @click="$router.push('/m/gameRecord')">
                        <div class="icon-wrap">
                          <svg class="am-icon am-icon-betting-record am-icon-md">
                            <use xlink:href="#betting-record"></use>
                          </svg>
                        </div>
                        <span class="member-nav-name">{{ $t('in_betting_record') }}</span>
                      </div>
                      &lt;!&ndash;                <div class="member-nav-item" @click="$router.push('/m/webEmail')">&ndash;&gt;
                      &lt;!&ndash;                  <div class="icon-wrap">&ndash;&gt;
                      &lt;!&ndash;                    <svg class="am-icon am-icon-icon-mail am-icon-md">&ndash;&gt;
                      &lt;!&ndash;                      <use xlink:href="#icon-mail"></use>&ndash;&gt;
                      &lt;!&ndash;                    </svg>&ndash;&gt;
                      &lt;!&ndash;                  </div>&ndash;&gt;
                      &lt;!&ndash;                  <p class="member-nav-name">{{ $t('news') }}</p>&ndash;&gt;
                      &lt;!&ndash;                </div>&ndash;&gt;
                      <div class="member-nav-item" @click="logout">
                        <div class="icon-wrap">
                          <svg class="am-icon am-icon-logout am-icon-md">
                            <use xlink:href="#logout"></use>
                          </svg>
                        </div>
                        <span class="member-nav-name">{{ $t("in_sign_out") }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <template #reference>
              <div class="member-icon" @click="$store.commit('setShowMobileSide', false)">
                <div class="member-wrap">
                  <div class="profile-icon">
                    <img
                        :src="`img/profile/icon_${$store.state.account.icon}.png`"
                        alt=""
                    />

                  </div>
                  <span class="wrap-text">VIP{{ $store.state.account.vip }}</span>
                </div>
                <svg
                    class="am-icon am-icon-side-arrow-down am-icon-md accordion-arrow"
                >
                  <use xlink:href="#side-arrow-down"></use>
                </svg>
              </div>
            </template>
          </van-popover>
        </div>
        <div class="login-register" v-else>
          <div class="nav-btn login-btn" @click="$router.push('/m/login')">
            <span>{{ $t('hd_login_button') }}</span>
          </div>
          <div
            class="nav-btn register-btn"
            @click="$router.push('/m/register')"
          >
            <span>{{ $t('hd_sign_button') }}</span>
          </div>
        </div>-->
      </div>
    </div>
  </div>
</template>

<style scoped>
::v-deep .van-popup {
  overflow-y: visible !important;
}
.home-header .header-layout .header-nav .download-icon {
  width: .58rem;
  height: .58rem;
  margin-left: .16rem;
  border-radius: 50%;
  background: #34363f url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAMAAABiM0N1AAAAaVBMVEVHcEyT0wCP0wD/zor/y2m11QC/1gD/kQHbzVim1QD/zHj/z43/qxD/z5Ki1AD/z5SX0wD/ylz/zX+41gD/ngX/zHKM0wD/zYr/y2b/rxHC1gCt1QD/yVP/x0X/vSSD0gD/kgP/xDT/rzekzeYvAAAAIHRSTlMA0pdr4LVjZhZIubjJ2f///////////////////////ch6QisAAAH4SURBVFjD7ZaLkoIgFEBBK9HK1EQzW639/49cQDMeFx/Yzs7sdLiYafcIN8wQ+rCKcKdBHD1Xne1H9Gui8/WshrOIZXftfBWvK0b0Mq0S6fy9qNba/xkRy1VxFnWch81K0dtG9AZRrDVXEcuNle4sUjS1g4iEHC9WJsbA4ng427ONO+pYph7e43D+lEap8VxRMtriZKaITI0o9mbWaJeMg2c/cb2ETcEayYInNx4b0G7JWhzxeMtWtb1ACxe3x5MKs7eL/9rgAiRcfMuSlqUlPJLiFZ7Dzb8TmYLkucVOPyOeMa/W7b8fwesLNJRJbk4F6leTmE/fZxSIKChlahlF38l4BtmfNI6+tJoGTTsUyD/qGXvmIiWAL5XpyVAgH0g4EbSHROVrFqHuAa9c7lEG4mtlkgrkwxkWUSTfdEqBUGQRUZr1kcm78jeEZQ+hnMwIRGEiZXlI+5ElwSaiG3jJbWyfRxcYeqEbAHbYkmATXS4pfDC1idI38T7RzYBmk1AzCxCVp0nKhyn6MsimRVnz0LMAEZ0W0cYwoYdBOi1Kq6pq1CzUGNzKSb6qhpkUAFH1PQnX8CaLuFvvd0GedyGoWJhIOegAnK+ERqISwTfiNOA8oCDnl321bpPfdReXdLb8+TJksggQCu6j5OP0nwrEz1awGoI+zOAHK3jEUSNyYroAAAAASUVORK5CYII=) no-repeat 50%/.36rem .36rem
}
</style>