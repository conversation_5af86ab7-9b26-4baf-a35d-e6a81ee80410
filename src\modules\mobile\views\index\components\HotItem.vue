<script>
import {play} from "@/mixins/play";
import {check} from "@/mixins/check";
import {menu} from "@/mixins/menu";
import {game} from "@/mixins/game";
import GameListItem from "@/modules/mobile/views/index/components/GameListItem.vue";

export default {
  name: "HotItem",
  components: {GameListItem},
  mixins:[play,check, menu, game],
  data() {
    return {
      more: false,
    }
  },
  computed: {
    params() {
      return this.$store.state.platform.hotGames
    },
    games() {
      if (this.more) {
        return this.$store.state.platform.hotGames.games;
      } else {
        return this.$store.state.platform.hotGames.games.slice(0, 9);
      }
    }
  },
}
</script>

<template>
  <div class="home-game-list">
    <div class="game-title">
      <div class="title-content">
        <div class="title-text">
<!--          <img
              class="game-icon"
              style="width: auto"
              :src="menuItem(params.categoryId).icon"
              alt=""
          />-->
          <span>{{ menuItem(params.categoryId).title }}</span>
        </div>
        <span class="see-all" @click="more = !more">{{ !more ? $t("more") : $t("less") }}</span>
      </div>
    </div>
    <ul class="game-list-wrap">
      <template v-for="(item, index) in games">
        <GameListItem :game="item" :key="index" />
      </template>
    </ul>
  </div>
<!--  <div class="hot-game-container" v-if="false">
    <div class="hot-game-content">
      <div class="game-title">
        <div class="title-content">
          <div class="title-text">
            <img
                class="game-icon"
                :src="menuItem(params.categoryId).icon"
                alt=""
            /><span>{{ menuItem(params.categoryId).title }}</span>
          </div>
          <span class="see-all" @click="more = !more">{{ !more ? $t("more") : $t("less") }}</span>
        </div>
      </div>
      <div class="hot-game-list hide-scrollbar">
        <template v-for="(item, index) in games">
          <div class="game-list-item" v-if="checkPlatAndGameStatus(item)" :key="index" @click="startMobileGame(item)">
            <div class="game-background">
                                <span
                                    class="lazy-load-image-background blur lazy-load-image-loaded"
                                    style="
                                    color: transparent;
                                    display: inline-block;
                                  "
                                ><img
                                    class="img-loading"
                                    v-lazy="item.icon"
                                /></span>
            </div>
          </div>
        </template>
      </div>
&lt;!&ndash;      <div class="more-hot-game" @click="showMoreGames">More Games</div>&ndash;&gt;
    </div>
  </div>-->
</template>

<style scoped>

</style>