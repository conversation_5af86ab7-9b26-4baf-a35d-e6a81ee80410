<script>
import {recharge} from "@/mixins/tdtc/recharge/recharge";
import Page1 from "@/modules/mobile/views/tdtc/recharge/Page1.vue";
import Page2 from "@/modules/mobile/views/tdtc/recharge/Page2.vue";
import Page3 from "@/modules/mobile/views/tdtc/recharge/Page3.vue";
import Page4 from "@/modules/mobile/views/tdtc/recharge/Page4.vue";
import Page5 from "@/modules/mobile/views/tdtc/recharge/Page5.vue";
import Page6 from "@/modules/mobile/views/tdtc/recharge/Page6.vue";
import Page7 from "@/modules/mobile/views/tdtc/recharge/Page7.vue";

export default {
  mixins: [recharge],
  components: {
    Page1,
    Page2,
    Page3,
    Page4,
    Page5,
    Page6,
    Page7,
  }
};
</script>

<template>
  <div class="withdraw-container-v2 td-recharge">
    <div class="mc-header-wrap">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-withdraw am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">{{ $t('recharge') }}</div>
        <div class="am-navbar-right">
          <svg @click="$router.push('/m/vouReport')"
              class="am-icon am-icon-voucher_list_b99e1f01 am-icon-md am-navbar-title"
          >
            <use xlink:href="#voucher_list_b99e1f01"></use>
          </svg>
        </div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="am-tabs am-tabs-top show-one">
      <div
          class="am-tabs-content am-tabs-content-animated"
          style="transform: translateX(0%) translateZ(0px)"
      >
        <div
            role="tabpanel"
            aria-hidden="false"
            class="am-tabs-tabpane am-tabs-tabpane-active"
        >
          <div>
            <div class="withdraw-main" style="padding-top: .15rem;">

              <div>
                <van-tabs v-draggable v-model="tabIndex" animated line-width="20%" line-height="2px" color="#FF8200" title-active-color="#FF8200" title-inactive-color="#312E2A" swipe-threshold="1">
                  <van-tab v-for="item in btnNum" :title="$t('RECHANGE_TITLE_'+item)" :key="item">
                    <component :is="`Page${item}`" :datas="datas" :key="item" />
                  </van-tab>
                </van-tabs>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
::v-deep .van-button {
  height: .68rem;
  border-radius: 0.1rem;
}
::v-deep .van-button__text {
  font-weight: 600;
  font-size: 0.26rem;
  color: #FFFFFF;
}
::v-deep .van-cell {
  font-weight: 600;
  font-size: 0.26rem;
  color: #312E2A;
}
</style>

<style lang="scss">

.td-recharge {


  /*
  .withdraw-main{
    height: 92vh;
  }
  ::v-deep .van-tab__pane {
    height: unset;
  }
  ::v-deep .van-cell {
    align-items: center;
  }
  */


/*  .van-tabs__nav {
    background-color: unset;
  }
  .van-tab__pane {
    height: 100vh;
    background: unset;
  }*/

  .van-cell {
    align-items: center;
  }
  .swiper-button-next {
    right: 0;
  }
  .swiper-button-prev {
    left: 0;
  }
  .swiper-button-next, .swiper-button-prev {
    width : .3rem;
    height : .4rem;
    margin-top: -0.12rem;
  }
  .swiper-container {
    margin: .2rem .36rem 0;
    font-size: 0.23rem;
    color: #b0b0b0;
    .swiper-active {
      color: #312E2A !important;
      //border: 1px solid #FF2222 !important;
      border: 1px solid #F9B749 !important;
      filter:unset !important;
      opacity:1 !important;
      //background: linear-gradient(-67deg, rgb(175, 106, 218), rgb(137, 194, 240)) !important;
    }
    .swiper-slide {
      width: auto;
      padding : .2rem .3rem;
      border-radius: 0.08rem;
      background: #FFFFFF;
      //background: linear-gradient(90deg, #c2c2c2 0, #8f8f8f 100%);
      //border: 1px solid #F9B749;
      border: 1px solid #b0b0b0;

      filter:grayscale(1);
      opacity:0.8;

      > div {
        display: flex;
      }
    }
  }

}

.withdraw-tip {
  width: 7.2rem;
  background: #F0DEDC;
  border-radius: 0.08rem;
  border: 0.02px solid #E6D3D7;margin: 0 auto;text-align: center;padding: .1rem;font-size: 0.23rem;
  color: #AF3841;
}
</style>
