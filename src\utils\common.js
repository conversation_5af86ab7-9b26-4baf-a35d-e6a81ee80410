/**
 * 防抖
 * @param fn 执行函数
 * @param ms 间隔毫秒数
 */
export const debounce = (fn, ms = 300) => {
  return (...args) => {
    if (window.lazy) {
      clearTimeout(window.lazy);
    }
    window.lazy = window.setTimeout(() => {
      fn(...args);
    }, ms);
  };
};


export const preloadImages = (imageUrls) => {
  imageUrls.forEach(url => {
    const img = new Image();
    img.src = url;
    img.onload = () => {
    };
    img.onerror = (error) => {
      console.error(`加载 ${url} 时出错:`, error);
    };
  });
}

export const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  });
}

export const FieldRenderType = {
  datetime: "datetime",
  datetime_s: "datetime_s",
  date: "date",
  hideStr: "hideStr",
  currency: "currency",
  formatGold: "formatGold",
  formatGoldWithK: "formatGoldWithK",
  customTemplate: "customTemplate",
}


export const getRandomInt = (min, max) => {
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

export const isMobile = () => {
  let Adaptive = /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i;
  return Adaptive.test(navigator.userAgent)
}

export const GAME_NAME = {
  6: "TRÁC KIM HOA",             // 诈金花
  50: "NGƯU NGƯU CƯỚP CÁI",      // 抢庄牛牛
  104: "BÁCH NHÂN NGƯU NGƯU",    // 百人牛牛
  122: "BACCARAT",               // 百家乐
  123: "PHI CẦM TẨU THÚ",        // 飞禽走兽
  130: "BẦU CUA",                // 鱼虾蟹
  131: "ĐẠI CHIẾN ĐỎ ĐEN",       // 红黑大战
  132: "RỒNG HỔ ĐẠI CHIẾN",      // 龙虎斗
  133: "XÓC ĐĨA",                // 色碟
  134: "TÀI XỈU",                // 骰宝
  135: "PIRATE KING",            // 加勒比海盗
  136: "THẦN TÀI",               // 金蝉
  138: "TÀI XỈU MD5",            // MD5骰宝
  140: "CỰC PHẨM PHI XE",        // 极品飞车
  141: "ROULETTE",               // ROULETTE
  142: "BIT GAME",               // btc game
  143: "SIC BO",                 //MEW SICBO
  144: "AVIATOR",                //aviator
  405: "BẮN CÁ",                 // 捕鱼
  516: "THẦN TÀI ĐẾN",           // 财神到
  517: "TUYỆT ĐỊA CẦU SINH",     // 绝地求生
  518: "AVENGERS",               // 复仇者联盟
  519: "TÂY DU KÝ",              // 西游记
  520: "NHIỀU TÀI NHIỀU PHÚC",   // 多福多彩
  521: "MA THÚ WOW",             // 魔兽世界
  522: "BUFFALO VALLEY",         // 野牛农场
  523: "CLASSIC FRUITS",         // 水果拉霸
  524: "GOLD COUNTRY",           // 黄金矿工
  525: "BARBARIAN NUDGE",        // 野蛮人
  526: "APES GO WILD",           // 猴子
  527: "WILD MONSTERS",          // 吸血鬼
  528: "IDOL'S BOUNTY",          // 神像
  529: "VIKING AXE",             // 维京人
  530: "TREASURES OF AZTEC",     // 阿兹特克神像
  602: "TIẾN LÊN MIỀN NAM",      // 南方跑得快
  604: "PHỎM",                   // PHỎM
  605: "BLACK JACK",              // 黑杰克
  706: "POKER TEXAS HOLD'EM",    // 德州扑克

  1000: "MINI BẦU CUA",          // 迷你鱼虾蟹
  1001: "MINI TÀI XỈU",          // 迷你骰宝
  1002: "MINI TRÊN DƯỚI",        // 迷你上下
  1003: "MINI KIM CƯƠNG",        // 迷你宝石
  1004: "MINI POKER",            // 迷你扑克拉巴
  1005: "MINI TÀI XỈU MD5",      // 迷你骰宝MD5
}

export const DATA_NAME = {
  // 0: "Rút ra TK",                           //"取款",
  // 1: "Gửi vào két",                         //"存款",
  2: "Đăng ký tài khoản tặng tiền",            //"注册赠送",
  3: "Thắng thua ",                            //"游戏输赢",
  4: "Rút tiền",                               //"兑换",
  5: "Cứu trợ",                                //"救援",
  6: "Nhiệm vụ",                               //"任务",
  7: "Tích điểm trưởng thành ",                //"成长基金",
  8: "Gói thưởng hội viên mới",                //"新人礼包",
  9: "Lĩnh thưởng hàng ngày",                  //"签到",
  10: "Mưa lì xì",                             //"红包雨",
  11: "Vòng quay may mắn",                     //"转盘",
  12: "Gói thưởng hội viên VIP",               //"vip奖励",
  13: "Lì xì nhiệm vụ đại lý",                 //"推广任务红包",
  14: "Đại lý rút tiền doanh thu",             //"代理返点提现",
  15: "Nạp tiền",                              //"充值",
  16: "Quản trị viên tặng thưởng",             //"管理员赠送"
  17: "Liên kết điện thoại",                   //"绑定手机号"
  18: "Nạp tiền lần đầu mỗi ngày",             //"每日首存"
  25: "Bonus",                                 //"Bonus"
  26: "Điểm cược mỗi ngày",                    //下注流水查询
  30: "Gift Code",                             //Gift Code
  38: "Bảng xếp hạng đại lý",                  //Gift Code
  41: "Jackpot",                               //Jackpot
  42: "Khuyến mãi nạp",                        //充值赠送
  43: "tiền thắng cược",                       //彩金赠送
  45: "Phần thưởng tích lũy nạp tiền",         //累计充值
  46: "Kiếm tiền bằng cách quảng cáo",         //推广得现金
}

export const GAME_TYPE = {
 /* ZJH: 6,                     //诈金花
  QZNN: 50,                   //抢庄牛牛
  BRNN: 104,                  //百人牛牛
  BJL: 122,                   //百家乐
  YXX: 130,                   //鱼虾蟹
  HHDZ: 131,                  //红黑大战
  LHDZ: 132,                  //龙虎斗
  SD: 133,                    //色碟
  SB: 134,                    //骰宝
  JLBHD: 135,                 //加勒比海盗
  JCLB: 136,                  //金蝉拉霸
  MDSB: 138,                  //MD5SB
  SICBO: 143,                 //MEW SICBO
  AVIATOR: 144,               //aviator
  DDZ: 200,                   //斗地主
  ERMJ: 392,                  //二人麻将
  DNTG: 405,                  //捕鱼
  LHJ: 516,                   //财神到
  PDK: 601,                   //跑得快
  NFPDK: 602,                 //南方跑得快
  PHOM: 604,                  //PHOM
  BLACKJACK: 605,             //blackjack
  DZPK: 706,                  //德州扑克
  JPFC: 140,                  //极品飞车,
  ROULETTE: 141,              //ROULETTE,
  BTC: 142,                   //btc,
  FQZS: 123,                  //飞禽走兽,
  PGS: 517,                   //绝地求生,
  FCZLM: 518,                 //复仇者联盟,
  XYJ: 519,                   //西游记
  DFDC: 520,                  //多福多彩
  WOWSLOT: 521,               //魔兽世界,
  YNNC: 522,                  //野牛农场,
  SGLB: 523,                  //水果拉霸,
  HJKG: 524,                  //黄金矿工,
  YMR: 525,                   //野蛮人,

  APES: 526,                  //猴子,
  WNGG: 527,                  //万能鬼怪,
  IDOL: 528,                  //神像,
  VIKING: 529,                //VIKING axe
  AZTEC: 530,                 //Treasures of Aztec*/

  SXBJL: 124,                 //视讯百家乐,
  ZQ: 901,                    //一球成名,
  CGCP: 902,                  //创信

  AWC: 903,
  BBIN_SX: 904,
  BBIN_TY: 905,
  BBIN_XTY: 906,
  CMD: 907,
  EBET: 908,
  PP: 909,
  DG: 910,
  MG: 911,
  PT: 912,
  UG: 913,
  SV128: 914,
  PG: 915,
  PP_SLOT: 916,
  CQ9_SLOT: 917,
  JILI: 918,
  SG: 919,
  WM: 920,
  // NEWSXBJL: 2000,             //
}



//title
export let getWebGameTitle = function (kindId) {
  if (kindId === GAME_TYPE.SXBJL) { return "AG LIVE CASINO" }
  else if (kindId === GAME_TYPE.ZQ) { return "SABA SPORTS" }
  else if (kindId === GAME_TYPE.CGCP) { return "CGCP" }

  else if (kindId === GAME_TYPE.AWC) { return "AWC LIVE CASINO" }
  else if (kindId === GAME_TYPE.BBIN_SX) { return "BBIN VIDEO" }
  else if (kindId === GAME_TYPE.BBIN_TY) { return "BBIN SPORTS" }
  else if (kindId === GAME_TYPE.BBIN_XTY) { return "BBIN NEW SPORTS" }
  else if (kindId === GAME_TYPE.CMD) { return "CMD" }
  else if (kindId === GAME_TYPE.EBET) { return "EBET" }
  else if (kindId === GAME_TYPE.PP) { return "PP" }
  else if (kindId === GAME_TYPE.DG) { return "DREAM GAMING" }
  else if (kindId === GAME_TYPE.MG) { return "MG" }
  else if (kindId === GAME_TYPE.PT) { return "PT" }
  else if (kindId === GAME_TYPE.UG) { return "UG" }
  else if (kindId === GAME_TYPE.SV128) { return "DIGMAAN" }
  else if (kindId === GAME_TYPE.PG) { return "PG SLOT" }
  else if (kindId === GAME_TYPE.PP_SLOT) { return "PP SLOT" }
  else if (kindId === GAME_TYPE.CQ9_SLOT) { return "CQ9 SLOT" }
  else if (kindId === GAME_TYPE.JILI) { return "JILI SLOT" }
  else if (kindId === GAME_TYPE.SG) { return "SPADEGAMING SLOT" }
  else if (kindId === GAME_TYPE.WM) { return "WM GAMING" }
}

// thirdPlatformCode
export let getFirmtype = function (kindId) {
  if (kindId  === GAME_TYPE.SXBJL) { return "AG" }
  else if (kindId === GAME_TYPE.ZQ) { return "SBTY" }
  else if (kindId === GAME_TYPE.CGCP) { return "CGCP" }

  else if (kindId === GAME_TYPE.AWC) { return "AWC" }
  else if (kindId === GAME_TYPE.BBIN_SX) { return "BBIN" }
  else if (kindId === GAME_TYPE.BBIN_TY) { return "BBIN" }
  else if (kindId === GAME_TYPE.BBIN_XTY) { return "BBIN" }
  else if (kindId === GAME_TYPE.CMD) { return "CMD" }
  else if (kindId === GAME_TYPE.EBET) { return "EBET" }
  else if (kindId === GAME_TYPE.PP) { return "PP" }
  else if (kindId === GAME_TYPE.DG) { return "DG" }
  else if (kindId === GAME_TYPE.MG) { return "MG" }
  else if (kindId === GAME_TYPE.PT) { return "PT" }
  else if (kindId === GAME_TYPE.UG) { return "NEWUG" }
  else if (kindId === GAME_TYPE.SV128) { return "SV128" }
  else if (kindId === GAME_TYPE.PG) { return "PG" }
  else if (kindId === GAME_TYPE.PP_SLOT) { return "PP" }
  else if (kindId === GAME_TYPE.CQ9_SLOT) { return "CQ9" }
  else if (kindId === GAME_TYPE.JILI) { return "JILI" }
  else if (kindId === GAME_TYPE.SG) { return "SG" }
  else if (kindId === GAME_TYPE.JILI) { return "JILI" }
  else if (kindId === GAME_TYPE.SG) { return "SG" }
  else if (kindId === GAME_TYPE.WM) { return "WM" }
}

// thirdGameCode
export let getFirmcode = function (kindId) {
  if (kindId === GAME_TYPE.SXBJL) { return "AG" }
  else if (kindId === GAME_TYPE.ZQ) { return "SBTY" }
  else if (kindId === GAME_TYPE.CGCP) { return "CGCP" }

  else if (kindId === GAME_TYPE.AWC) { return "AWC" }
  else if (kindId === GAME_TYPE.BBIN_SX) { return "60" }
  else if (kindId === GAME_TYPE.BBIN_TY) { return "61" }
  else if (kindId === GAME_TYPE.BBIN_XTY) { return "64" }
  else if (kindId === GAME_TYPE.CMD) { return "CMD" }
  else if (kindId === GAME_TYPE.EBET) { return "EBET" }
  else if (kindId === GAME_TYPE.PP) { return "101" }
  else if (kindId === GAME_TYPE.DG) { return "DG" }
  else if (kindId === GAME_TYPE.MG) { return "1921" }
  else if (kindId === GAME_TYPE.PT) { return "live" }
  else if (kindId === GAME_TYPE.UG) { return "NEWUG" }
  else if (kindId === GAME_TYPE.SV128) { return "SV128" }
  else if (kindId === GAME_TYPE.PG) { return "PG" }
  else if (kindId === GAME_TYPE.PP_SLOT) { return "PP" }
  else if (kindId === GAME_TYPE.CQ9_SLOT) { return "CQ9" }
  else if (kindId === GAME_TYPE.JILI) { return "JILI" }
  else if (kindId === GAME_TYPE.SG) { return "SG" }
  else if (kindId === GAME_TYPE.WM) { return "WM" }
}


//本地存储
export const LocalStorage = {
  TOKEN: "token", //token
  BUG_REPORTT_TIME: "bug-report-time",

  SOUND_EFFECT_SETTING: "sound-effect-volume",                       //声音大小
  SOUND_MUSIC_SETTING: "sound-music-volume",                        //声音大小
  ABLE_EFFECT_SETTING: "sound-effect-able",                         //声音开关
  ABLE_MUSIC_SETTING: "sound-music-able",                          //声音开关

  USER_LOCAL_INFO: "user-local-info",                           // 用户本地信息
  USER_LOCAL_ACCOUNT_INFO: "user-local-accountinfo",                    // 用户本地信息
  LOCAL_RECENTGAME_INFO: "user-local-recentgame",                     // 最近遊戲
  LOCAL_GAME_VERSION: "user-local-gameversion",                     // 最近遊戲
  LOCAL_ENTER_TYPE: "user-local-lastEnterType",                     // 最近遊戲

  PACKAGE_URL_CLIENT: "package_url_client",
  PACKAGE_URL_GAMES: "package_url_games",
  PACKAGE_URL_CONNECT: "package_url_connect",

  PACKAGE_URL_A_NEW: "user_local_psw1",
  PACKAGE_URL_B_NEW: "user_local_psw2",
  PACKAGE_URL_C_NEW: "user_local_psw3",
  PACKAGE_URL_D_NEW: "user_local_psw4",
  PACKAGE_URL_E_NEW: "user_local_psw5",
  PACKAGE_URL_F_NEW: "user_local_psw6",
  PACKAGE_URL_G_NEW: "user_local_psw7",
}



// 本地测试 auth
export const LOCAL_AUTH_URL = [
  "http://192.168.8.128:8000",
]

// 本地测试 auth
export const LOCAL_QUERY_URL = [
  "http://192.168.8.114:5029/query",
]

// 正式服 - auth/cp/api/connect/help
export const REMOTE_CONNECT_URL = [
  "tdtcwork.com",
]

//其他配置
export const REMOTE_AUTH_UNIVERSAL = `.au.`;
export const REMOTE_CONNECT_UNIVERSAL = `.cq.`;
export const REMOTE_RECHARGE_UNIVERSAL = `.aa.`;
export const REMOTE_CUSTOMER_UNIVERSAL = `.au.`;
export const REMOTE_QUERY_UNIVERSAL = `.cq.`;
export const REMOTE_THIRDGAME_UNIVERSAL = `.aa.`;

export const REMOTE_CLIENT_DOMAIN = `-a-a-0-`;  //根据大厅地址调整 aa0 = res0 // aa1 = res45 // aa2 = res44 // aa3 = res4b
export const REMOTE_GAMES_DOMAIN = `-a-a-0-`;  //根据子游戏地址调整  aa0 = res0 // aa1 = res45 // aa2 = res44 // aa3 = res4b

export const AUTH_DOMAIN = `-b-b-0-`;
export const QUERY_DOMAIN = `-b-b-1-`;
export const CONNECT_DOMAIN = `-a-b-0-`;
export const CP_DOMAIN = `-b-a-1-`;
export const API_DOMAIN = `-b-a-0-`;

//泛解析
const chars = 'cdefghijklmnopqrstuvwxyz56789';
export let initDomain = function (domain) {
  let maxLen = chars.length;
  // 替换域名的中'-'字符
  while (~domain.indexOf('-')) {
    // 随机插入1到5个字符
    let rand = (Math.random() * 5 + 1) | 0;
    let str = '';
    for (let i = 0; i < rand; i++) {
      let idx = Math.random() * maxLen | 0;
      str += chars[idx];
    }
    // 随机字符替换'-'字符
    domain = domain.replace('-', str);
  }

  return domain
}



let getAuthDomain = function () {
  let content = localStorage.getItem(LocalStorage.PACKAGE_URL_B_NEW);
  let domain = content || ""
  if (domain === "") {
    domain = initDomain(AUTH_DOMAIN)
    localStorage.setItem(LocalStorage.PACKAGE_URL_B_NEW, domain);
  }
  return domain
}

let getQueryDomain = function () {
  let content = localStorage.getItem(LocalStorage.PACKAGE_URL_C_NEW);
  let domain = content || ""
  if (domain === "") {
    domain = initDomain(QUERY_DOMAIN)
    localStorage.setItem(LocalStorage.PACKAGE_URL_C_NEW, domain);
  }
  return domain
}

let getCPDomain = function () {
  let content = localStorage.getItem(LocalStorage.PACKAGE_URL_E_NEW);
  let domain = content || ""
  if (domain === "") {
    domain = initDomain(CP_DOMAIN)
    localStorage.setItem(LocalStorage.PACKAGE_URL_E_NEW, domain);
  }
  return domain
}

let getAPIDomain = function () {
  let content = localStorage.getItem(LocalStorage.PACKAGE_URL_F_NEW);
  let domain = content || ""
  if (domain === "") {
    domain = initDomain(API_DOMAIN)
   localStorage.setItem(LocalStorage.PACKAGE_URL_F_NEW, domain);
  }
  return domain
}

export let ShiftConnectPackageUrl = function () {
  let content = localStorage.getItem(LocalStorage.PACKAGE_URL_CONNECT);
  let index = parseInt(content) || 0;
  index = index + 1;
  localStorage.setItem(LocalStorage.PACKAGE_URL_CONNECT, "" + index);
  return index;
}

export let getConnectPackageUrlIndex = function () {
  let content = localStorage.getItem(LocalStorage.PACKAGE_URL_CONNECT);
  let index = parseInt(content) || 0;
  index = index % REMOTE_CONNECT_URL.length;
  return index;
}

//获取认证地址
export let getAuthUrl = function () {
  let index = 0;
  if (process.env.VUE_APP_ENV === "production") {
    index = getConnectPackageUrlIndex();
    return "https://" + getAuthDomain() + REMOTE_AUTH_UNIVERSAL + REMOTE_CONNECT_URL[index];
  } else {
    return LOCAL_AUTH_URL[index];
  }
}

// 获取充值API
export let getRechargeUrl = function () {
  if (process.env.VUE_APP_ENV === "production") {
    let index = getConnectPackageUrlIndex();
    return "https://" + getAPIDomain() + REMOTE_RECHARGE_UNIVERSAL + REMOTE_CONNECT_URL[index];
  } else {
    return "http://ysq-rech.lvh.me";
  }
}

// 获取客服信息
export let getCustomerInfoUrl = function () {
  let index = 0;
  if (process.env.VUE_APP_ENV === "production") {
    index = getConnectPackageUrlIndex();
    return "https://" + getAuthDomain() + REMOTE_CUSTOMER_UNIVERSAL + REMOTE_CONNECT_URL[index] + "/notice/config";
  } else {
    return LOCAL_AUTH_URL[index] + "/notice/config";
  }
}

// 查询用户信息
export let getQueryInfoUrl = function () {
  let index = 0;
  if (process.env.VUE_APP_ENV === "production") {
    index = getConnectPackageUrlIndex();
    return "https://" + getQueryDomain() + REMOTE_QUERY_UNIVERSAL + REMOTE_CONNECT_URL[index] + "/query";
  } else {
    return LOCAL_QUERY_URL[index];
  }
}

// 三方游戏
export let getThirdGameUrl = function () {
  if (process.env.VUE_APP_ENV === "production") {
    let index = getConnectPackageUrlIndex();
    return "https://" + getCPDomain() + REMOTE_THIRDGAME_UNIVERSAL + REMOTE_CONNECT_URL[index] + "/thirdgames";
  } else {
    return "";
  }
}